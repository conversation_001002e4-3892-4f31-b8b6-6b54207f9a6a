{"train_batch_size": 256, "train_micro_batch_size_per_gpu": 16, "gradient_accumulation_steps": 4, "optimizer": {"type": "AdamW", "params": {"lr": 0.0001, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 0.0001, "warmup_num_steps": 1000}}, "zero_optimization": {"stage": 2, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "allgather_partitions": true, "allgather_bucket_size": 200000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 200000000.0, "contiguous_gradients": true}, "fp16": {"enabled": true, "loss_scale": 0, "loss_scale_window": 1000, "initial_scale_power": 16, "hysteresis": 2, "min_loss_scale": 1}, "gradient_clipping": 1.0, "steps_per_print": 50, "wall_clock_breakdown": false}