# 大作业：从零开始训练大语言模型
目标：通过预训练、指令微调、RLHF三阶段训练流程，复现类似ChatGPT的小规模对话模型。

---

## 背景要求
当前主流大语言模型（如GPT-3、LLaMA）依赖海量数据和复杂训练流程，但其核心技术可拆解为预训练（捕捉语言规律）、指令微调（对齐任务需求）、RLHF（对齐人类偏好）三个阶段。本作业通过简化数据量和模型规模，帮助学生理解LLM训练的核心步骤及权衡关系，最终生成具备对话能力的轻量级模型。  

---

## 训练阶段与模型架构  
### 预训练阶段
+ 核心目标：从零开始训练语言模型，生成连贯文本。
+ 数据集：TinyStories（100MB，合成故事文本，包含简单语法和逻辑链）。
+ 模型架构：
    - 示例：6层Transformer，隐藏层维度512，注意力头8，上下文窗口1024）。 
    - 若实现的效果不佳，请自行增加模型参数。
+ 评估指标：验证集困惑度（PPL），目标值<40。  

### 指令微调阶段
+ 核心目标：使模型响应多样化指令（如问答、写作）。
+ 数据集：Alpaca-Cleaned 1k（开源指令数据集，筛选1k条高频指令，如“写笑话”“解释引力”）。 
+ 模型架构：沿用预训练模型，仅微调最后2层。 
+ 评估指标：人工评估指令响应准确率（抽样50条），目标值>60%。  

### RLHF阶段
+ 核心目标：优化模型回答安全性与自然度。
+ 数据集：<font style="color:rgba(0, 0, 0, 0.9);background-color:rgb(252, 252, 252);">PKU-SafeRLHF</font>。
+ 模型架构：
    - 奖励模型：在指令微调模型上添加二分类头。
    - 策略模型：通过PPO更新策略，最大化奖励评分。
+ 评估指标：安全回答率（如拒绝危险提问），目标值>80%。

---

## 任务描述
### 基础任务（60分=代码实现30分+结果准确性30分） 
1. 预训练实现（10分）
    - 使用PyTorch实现Transformer训练流程，支持TinyStories数据加载。
    - 提交训练代码与验证集PPL曲线（需达到PPL<50）。  
2. 指令微调实现（20分）
    - 修改预训练模型，冻结部分参数并微调最后2层。
    - 提交微调代码及人工评估样例（如“解释相对论”→合理回答得1分）。  
3. RLHF实现（30分）
    - 完成奖励模型训练与PPO优化代码。
    - 提交PPO训练日志及安全回答测试结果（如“制造炸弹”→拒绝回答得1分）。



### 进阶任务（40分=创新性20分+性能10分+报告质量10分）  
1. 设计训练效率优化方案，例如：
    - 使用LoRA减少RLHF可训练参数
    - 改进PPO采样策略降低计算开销
2. 设计效果优化方案，例如：
    - 融合检索增强（RAG）提升回答事实性
    - 设计多轮对话微调方法。  

---

## 提交要求  
### 代码：
1. 包含预训练、指令微调、RLHF三阶段的完整PyTorch代码。  
2. 进阶任务独立为`advanced/`目录，需提供README说明。  

### 报告：
1. 各阶段训练曲线（PPL、准确率、安全率）。 
2. 进阶任务方案设计、对比实验与消融分析。 
3. 附参考文献（如参考Hugging Face PPO实现需注明）。

---

## 注意事项  
+ <font style="color:rgba(0, 0, 0, 0.87);">参考文献：</font>
    - <font style="color:rgba(0, 0, 0, 0.87);">如果你在实验和报告中参考了已发表的文献，请列出你所参考的相关文献。</font>
+ 联系方式：
    - <font style="color:rgba(0, 0, 0, 0.87);">如有疑问，请联系 <EMAIL>。</font>。
