Quick solution is to modify the following file:
<path-to-anaconda-environment>/lib/python3.8/site-packages/deepspeed/elasticity/elastic_agent.py

Comment out the following
from torch.distributed.elastic.agent.server.api import _get_socket_with_port

Then add the following which I just lifted from old version of pytorch

（/home/<USER>/miniconda3/envs/gpt-training/lib/python3.10/site-packages/deepspeed/elasticity/elastic_agent.py
）

# solving bugs of socket
import socket
def _get_socket_with_port() -> socket.socket:
    """Return a free port on localhost.

    The free port is "reserved" by binding a temporary socket on it.
    Close the socket before passing the port to the entity that
    requires it. Usage example::

    sock = _get_socket_with_port()
    with closing(sock):
        port = sock.getsockname()[1]
        sock.close()
        # there is still a race-condition that some other process
        # may grab this port before func() runs
        func(port)
    """
    addrs = socket.getaddrinfo(
        host="localhost", port=None, family=socket.AF_UNSPEC, type=socket.SOCK_STREAM
    )
    for addr in addrs:
        family, type, proto, _, _ = addr
        s = socket.socket(family, type, proto)
        try:
            s.bind(("localhost", 0))
            s.listen(0)
            return s
        except OSError as e:
            s.close()
            log.info("Socket creation attempt failed.", exc_info=e)
    raise RuntimeError("Failed to create a socket")
