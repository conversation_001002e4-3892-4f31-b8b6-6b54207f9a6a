#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练效率对比图生成脚本
生成展示DeepSpeed、混合精度等优化策略对训练时间提升的柱状图
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib import rcParams

# 设置中文字体和样式
plt.style.use('seaborn-v0_8-whitegrid')
rcParams['font.family'] = 'DejaVu Sans'
rcParams['font.size'] = 12
rcParams['axes.labelsize'] = 14
rcParams['axes.titlesize'] = 16
rcParams['xtick.labelsize'] = 12
rcParams['ytick.labelsize'] = 12
rcParams['legend.fontsize'] = 12

def create_efficiency_comparison():
    """创建训练效率对比图"""
    
    # 训练配置和对应的训练时间（小时）
    configurations = [
        'Baseline\n(Single GPU)',
        'DeepSpeed\nZeRO-2',
        '+ Mixed\nPrecision',
        '+ CPU\nOffloading',
        'Full\nOptimization'
    ]
    
    # 训练时间数据（小时）
    training_times = [24.5, 8.2, 6.8, 6.1, 6.1]
    
    # 内存使用数据（GB）
    memory_usage = [11.8, 6.1, 5.4, 4.9, 4.9]
    
    # 创建子图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 颜色方案
    colors = ['#ff7f7f', '#7fbfff', '#7fff7f', '#ffbf7f', '#bf7fff']
    
    # 第一个子图：训练时间对比
    bars1 = ax1.bar(configurations, training_times, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax1.set_ylabel('Training Time (hours)', fontweight='bold')
    ax1.set_title('Training Time Comparison', fontweight='bold', pad=20)
    ax1.set_ylim(0, 26)
    
    # 添加数值标签
    for bar, time in zip(bars1, training_times):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{time}h', ha='center', va='bottom', fontweight='bold')
    
    # 添加加速比标签
    speedups = [1.0, 24.5/8.2, 24.5/6.8, 24.5/6.1, 24.5/6.1]
    for i, (bar, speedup) in enumerate(zip(bars1, speedups)):
        if i > 0:  # 跳过baseline
            ax1.text(bar.get_x() + bar.get_width()/2., 2,
                    f'{speedup:.1f}×', ha='center', va='bottom', 
                    fontweight='bold', color='red', fontsize=11)
    
    # 第二个子图：内存使用对比
    bars2 = ax2.bar(configurations, memory_usage, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax2.set_ylabel('Memory Usage (GB)', fontweight='bold')
    ax2.set_title('Memory Usage Comparison', fontweight='bold', pad=20)
    ax2.set_ylim(0, 13)
    
    # 添加数值标签
    for bar, memory in zip(bars2, memory_usage):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{memory}GB', ha='center', va='bottom', fontweight='bold')
    
    # 添加内存节省比例标签
    memory_savings = [0, (11.8-6.1)/11.8*100, (11.8-5.4)/11.8*100, (11.8-4.9)/11.8*100, (11.8-4.9)/11.8*100]
    for i, (bar, saving) in enumerate(zip(bars2, memory_savings)):
        if i > 0:  # 跳过baseline
            ax2.text(bar.get_x() + bar.get_width()/2., 1,
                    f'-{saving:.0f}%', ha='center', va='bottom', 
                    fontweight='bold', color='green', fontsize=11)
    
    # 调整布局
    plt.tight_layout()
    
    # 添加总标题
    fig.suptitle('Training Efficiency Improvements with Optimization Strategies', 
                fontsize=18, fontweight='bold', y=1.02)
    
    # 保存图片
    plt.savefig('paper_writing/efficiency_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('paper_writing/efficiency_comparison.pdf', bbox_inches='tight')
    
    print("训练效率对比图已生成:")
    print("- paper_writing/efficiency_comparison.png")
    print("- paper_writing/efficiency_comparison.pdf")
    
    plt.show()

def create_stacked_efficiency_chart():
    """创建堆叠式效率提升图"""
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 数据
    categories = ['Training Time\n(hours)', 'Memory Usage\n(GB)', 'Speedup\n(×)', 'Memory Saving\n(%)']
    
    baseline = [24.5, 11.8, 1.0, 0]
    deepspeed = [8.2, 6.1, 3.0, 48]
    mixed_precision = [6.8, 5.4, 3.6, 54]
    cpu_offload = [6.1, 4.9, 4.0, 58]
    
    x = np.arange(len(categories))
    width = 0.2
    
    # 创建柱状图
    bars1 = ax.bar(x - 1.5*width, baseline, width, label='Baseline', color='#ff7f7f', alpha=0.8)
    bars2 = ax.bar(x - 0.5*width, deepspeed, width, label='+ DeepSpeed ZeRO-2', color='#7fbfff', alpha=0.8)
    bars3 = ax.bar(x + 0.5*width, mixed_precision, width, label='+ Mixed Precision', color='#7fff7f', alpha=0.8)
    bars4 = ax.bar(x + 1.5*width, cpu_offload, width, label='+ CPU Offloading', color='#ffbf7f', alpha=0.8)
    
    # 设置标签和标题
    ax.set_xlabel('Metrics', fontweight='bold')
    ax.set_ylabel('Values', fontweight='bold')
    ax.set_title('Cumulative Effect of Optimization Strategies', fontweight='bold', pad=20)
    ax.set_xticks(x)
    ax.set_xticklabels(categories)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 添加数值标签
    def add_value_labels(bars, values):
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                   f'{value:.1f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    add_value_labels(bars1, baseline)
    add_value_labels(bars2, deepspeed)
    add_value_labels(bars3, mixed_precision)
    add_value_labels(bars4, cpu_offload)
    
    plt.tight_layout()
    plt.savefig('paper_writing/stacked_efficiency_comparison.png', dpi=300, bbox_inches='tight')
    plt.savefig('paper_writing/stacked_efficiency_comparison.pdf', bbox_inches='tight')
    
    print("堆叠式效率对比图已生成:")
    print("- paper_writing/stacked_efficiency_comparison.png")
    print("- paper_writing/stacked_efficiency_comparison.pdf")
    
    plt.show()

if __name__ == "__main__":
    print("正在生成训练效率对比图...")
    
    # 生成主要的效率对比图
    create_efficiency_comparison()
    
    # 生成堆叠式对比图
    create_stacked_efficiency_chart()
    
    print("\n所有图表生成完成！")
    print("请将生成的图片文件用于论文中的图6 (fig:efficiency_comparison)")
