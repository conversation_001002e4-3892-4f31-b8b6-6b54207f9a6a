import os
import sys

__package__ = "trainer"
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import argparse
import time
import math
import warnings
import torch
import torch.distributed as dist
from contextlib import nullcontext
from torch import optim, nn
from torch.nn.parallel import DistributedDataParallel
from torch.utils.data import DataLoader, DistributedSampler, random_split
from transformers import AutoTokenizer, AutoModelForCausalLM
from advance.model.model_main import MainConfig, MainForCausalLM
from dataset.lm_dataset import SFTDataset

warnings.filterwarnings('ignore')


def Logger(content):
    if not ddp or dist.get_rank() == 0:
        print(content)


def get_lr(current_step, total_steps, lr):
    return lr / 10 + 0.5 * lr * (1 + math.cos(math.pi * current_step / total_steps))


def calculate_reasoning_metrics(logits, targets, loss_mask, tokenizer):
    """计算推理模型特定的评估指标"""
    with torch.no_grad():
        # 基础准确率计算
        predictions = torch.argmax(logits, dim=-1)
        correct = (predictions == targets) * loss_mask
        accuracy = correct.sum().float() / loss_mask.sum().float()

        # 推理步骤准确率 - 检查<think>和</think>标签之间的内容
        think_start_ids = tokenizer('<think>').input_ids
        think_end_ids = tokenizer('</think>').input_ids
        answer_start_ids = tokenizer('<answer>').input_ids
        answer_end_ids = tokenizer('</answer>').input_ids

        # 计算推理步骤的准确率
        reasoning_accuracy = 0.0
        answer_accuracy = 0.0

        # 简化的推理质量评估 - 检查特殊标签的预测准确性
        special_tokens = set(think_start_ids + think_end_ids + answer_start_ids + answer_end_ids)
        special_mask = torch.zeros_like(targets, dtype=torch.bool)
        for token_id in special_tokens:
            special_mask |= (targets == token_id)

        if special_mask.sum() > 0:
            special_correct = correct * special_mask
            reasoning_accuracy = special_correct.sum().float() / special_mask.sum().float()

        # 答案部分准确率 - 简化版本
        answer_accuracy = accuracy  # 暂时使用整体准确率作为答案准确率

        return {
            'accuracy': accuracy.item(),
            'reasoning_accuracy': reasoning_accuracy.item() if isinstance(reasoning_accuracy, torch.Tensor) else reasoning_accuracy,
            'answer_accuracy': answer_accuracy.item(),
            'reasoning_step_quality': (reasoning_accuracy.item() if isinstance(reasoning_accuracy, torch.Tensor) else reasoning_accuracy) * 100
        }


def evaluate_model(model, val_loader, tokenizer, device, ctx):
    """验证集评估函数"""
    model.eval()
    total_loss = 0.0
    total_tokens = 0
    total_accuracy = 0.0
    total_reasoning_accuracy = 0.0
    total_answer_accuracy = 0.0

    # 推理标签
    start_of_think_ids = tokenizer('<think>').input_ids
    end_of_think_ids = tokenizer('</think>').input_ids
    start_of_answer_ids = tokenizer('<answer>').input_ids
    end_of_answer_ids = tokenizer('</answer>').input_ids
    loss_fct = nn.CrossEntropyLoss(reduction='none')

    with torch.no_grad():
        for step, (X, Y, loss_mask) in enumerate(val_loader):
            X = X.to(device)
            Y = Y.to(device)
            loss_mask = loss_mask.to(device)

            with ctx:
                res = model(X)
                loss = loss_fct(
                    res.logits.view(-1, res.logits.size(-1)),
                    Y.view(-1)
                ).view(Y.size())

                # 应用特殊标签的额外权重
                sp_ids = torch.isin(Y.view(-1),
                                    torch.tensor(start_of_think_ids + end_of_think_ids
                                                 + start_of_answer_ids + end_of_answer_ids
                                                 ).to(device))
                loss_mask_weighted = loss_mask.view(-1).clone()
                loss_mask_sum = loss_mask.sum()
                loss_mask_weighted[sp_ids] = 10
                loss_mask_weighted = loss_mask_weighted.view(Y.size())
                loss = (loss * loss_mask_weighted).sum() / loss_mask_sum
                loss += res.aux_loss

            # 计算指标
            metrics = calculate_reasoning_metrics(res.logits, Y, loss_mask, tokenizer)

            total_loss += loss.item() * loss_mask.sum().item()
            total_tokens += loss_mask.sum().item()
            total_accuracy += metrics['accuracy'] * loss_mask.sum().item()
            total_reasoning_accuracy += metrics['reasoning_accuracy'] * loss_mask.sum().item()
            total_answer_accuracy += metrics['answer_accuracy'] * loss_mask.sum().item()

            # 限制验证步数，避免验证时间过长
            if step >= 50:
                break

    avg_loss = total_loss / total_tokens if total_tokens > 0 else 0.0
    avg_accuracy = total_accuracy / total_tokens if total_tokens > 0 else 0.0
    avg_reasoning_accuracy = total_reasoning_accuracy / total_tokens if total_tokens > 0 else 0.0
    avg_answer_accuracy = total_answer_accuracy / total_tokens if total_tokens > 0 else 0.0
    ppl = math.exp(avg_loss) if avg_loss < 10 else float('inf')

    model.train()
    return {
        'val_loss': avg_loss,
        'val_ppl': ppl,
        'val_accuracy': avg_accuracy,
        'val_reasoning_accuracy': avg_reasoning_accuracy,
        'val_answer_accuracy': avg_answer_accuracy,
        'reasoning_improvement': avg_reasoning_accuracy * 100  # 推理能力提升百分比
    }


def train_epoch(epoch, wandb, val_loader):
    # 思考标签占位符
    start_of_think_ids = tokenizer('<think>').input_ids
    end_of_think_ids = tokenizer('</think>').input_ids
    start_of_answer_ids = tokenizer('<answer>').input_ids
    end_of_answer_ids = tokenizer('</answer>').input_ids
    loss_fct = nn.CrossEntropyLoss(reduction='none')
    start_time = time.time()

    # 训练指标累积
    epoch_loss = 0.0
    epoch_tokens = 0
    epoch_accuracy = 0.0
    epoch_reasoning_accuracy = 0.0

    for step, (X, Y, loss_mask) in enumerate(train_loader):
        X = X.to(args.device)
        Y = Y.to(args.device)
        loss_mask = loss_mask.to(args.device)
        lr = get_lr(epoch * iter_per_epoch + step, args.epochs * iter_per_epoch, args.learning_rate)
        for param_group in optimizer.param_groups:
            param_group['lr'] = lr

        with ctx:
            res = model(X)
            loss = loss_fct(
                res.logits.view(-1, res.logits.size(-1)),
                Y.view(-1)
            ).view(Y.size())
            sp_ids = torch.isin(Y.view(-1),
                                torch.tensor(start_of_think_ids + end_of_think_ids
                                             + start_of_answer_ids + end_of_answer_ids
                                             ).to(args.device))
            # 在 sp_ids 对应的位置增加额外的惩罚
            loss_mask_weighted = loss_mask.view(-1).clone()
            loss_mask_sum = loss_mask.sum()
            loss_mask_weighted[sp_ids] = 10
            loss_mask_weighted = loss_mask_weighted.view(Y.size())
            loss = (loss * loss_mask_weighted).sum() / loss_mask_sum
            loss += res.aux_loss
            loss = loss / args.accumulation_steps

        # 计算训练指标
        metrics = calculate_reasoning_metrics(res.logits, Y, loss_mask, tokenizer)
        epoch_loss += loss.item() * args.accumulation_steps * loss_mask.sum().item()
        epoch_tokens += loss_mask.sum().item()
        epoch_accuracy += metrics['accuracy'] * loss_mask.sum().item()
        epoch_reasoning_accuracy += metrics['reasoning_accuracy'] * loss_mask.sum().item()

        scaler.scale(loss).backward()

        if (step + 1) % args.accumulation_steps == 0:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)

            scaler.step(optimizer)
            scaler.update()

            optimizer.zero_grad(set_to_none=True)

        if step % args.log_interval == 0:
            spend_time = time.time() - start_time
            current_loss = loss.item() * args.accumulation_steps
            current_ppl = math.exp(current_loss) if current_loss < 10 else float('inf')

            Logger(
                'Epoch:[{}/{}]({}/{}) loss:{:.3f} ppl:{:.3f} acc:{:.3f} reasoning_acc:{:.3f} lr:{:.12f} epoch_Time:{}min:'.format(
                    epoch + 1,
                    args.epochs,
                    step,
                    iter_per_epoch,
                    current_loss,
                    current_ppl,
                    metrics['accuracy'],
                    metrics['reasoning_accuracy'],
                    optimizer.param_groups[-1]['lr'],
                    spend_time / (step + 1) * iter_per_epoch // 60 - spend_time // 60))

            if (wandb is not None) and (not ddp or dist.get_rank() == 0):
                wandb.log({
                    "train/loss": current_loss,
                    "train/ppl": current_ppl,
                    "train/accuracy": metrics['accuracy'],
                    "train/reasoning_accuracy": metrics['reasoning_accuracy'],
                    "train/answer_accuracy": metrics['answer_accuracy'],
                    "train/reasoning_step_quality": metrics['reasoning_step_quality'],
                    "lr": optimizer.param_groups[-1]['lr'],
                    "epoch": epoch + 1,
                    "step": epoch * iter_per_epoch + step
                })

        # 验证集评估
        if step % args.eval_interval == 0 and step > 0:
            if val_loader is not None:
                val_metrics = evaluate_model(model, val_loader, tokenizer, args.device, ctx)
                Logger(f"Validation - Loss: {val_metrics['val_loss']:.3f}, PPL: {val_metrics['val_ppl']:.3f}, "
                      f"Acc: {val_metrics['val_accuracy']:.3f}, Reasoning Acc: {val_metrics['val_reasoning_accuracy']:.3f}")

                if (wandb is not None) and (not ddp or dist.get_rank() == 0):
                    wandb.log({
                        "val/loss": val_metrics['val_loss'],
                        "val/ppl": val_metrics['val_ppl'],
                        "val/accuracy": val_metrics['val_accuracy'],
                        "val/reasoning_accuracy": val_metrics['val_reasoning_accuracy'],
                        "val/answer_accuracy": val_metrics['val_answer_accuracy'],
                        "val/reasoning_improvement": val_metrics['reasoning_improvement'],
                        "step": epoch * iter_per_epoch + step
                    })

        if (step + 1) % args.save_interval == 0 and (not ddp or dist.get_rank() == 0):
            model.eval()
            moe_path = '_moe' if lm_config.use_moe else ''
            ckp = f'{args.save_dir}/reason_{lm_config.hidden_size}{moe_path}.pth'

            if isinstance(model, torch.nn.parallel.DistributedDataParallel):
                state_dict = model.module.state_dict()
            else:
                state_dict = model.state_dict()

            state_dict = {k: v.half() for k, v in state_dict.items()}  # 半精度保存
            torch.save(state_dict, ckp)
            model.train()

    # 记录epoch级别的指标
    if epoch_tokens > 0:
        avg_epoch_loss = epoch_loss / epoch_tokens
        avg_epoch_accuracy = epoch_accuracy / epoch_tokens
        avg_epoch_reasoning_accuracy = epoch_reasoning_accuracy / epoch_tokens
        avg_epoch_ppl = math.exp(avg_epoch_loss) if avg_epoch_loss < 10 else float('inf')

        if (wandb is not None) and (not ddp or dist.get_rank() == 0):
            wandb.log({
                "epoch/loss": avg_epoch_loss,
                "epoch/ppl": avg_epoch_ppl,
                "epoch/accuracy": avg_epoch_accuracy,
                "epoch/reasoning_accuracy": avg_epoch_reasoning_accuracy,
                "epoch/reasoning_improvement": avg_epoch_reasoning_accuracy * 100,
                "epoch_num": epoch + 1
            })


def init_model(lm_config):
    tokenizer = AutoTokenizer.from_pretrained('../model')
    model = MainForCausalLM(lm_config)
    moe_path = '_moe' if lm_config.use_moe else ''
    ckp = f'{args.save_dir}/rlhf_{lm_config.hidden_size}{moe_path}.pth'
    state_dict = torch.load(ckp, map_location=args.device)
    model.load_state_dict(state_dict, strict=False)
    Logger(f'LLM总参数量：{sum(p.numel() for p in model.parameters() if p.requires_grad) / 1e6:.3f} 百万')
    model = model.to(args.device)
    return model, tokenizer


def init_distributed_mode():
    if not ddp: return
    global ddp_local_rank, DEVICE

    dist.init_process_group(backend="nccl")
    ddp_rank = int(os.environ["RANK"])
    ddp_local_rank = int(os.environ["LOCAL_RANK"])
    ddp_world_size = int(os.environ["WORLD_SIZE"])
    DEVICE = f"cuda:{ddp_local_rank}"
    torch.cuda.set_device(DEVICE)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Main Distill Reasoning")
    parser.add_argument("--out_dir", type=str, default="../out")
    parser.add_argument("--epochs", type=int, default=1)
    parser.add_argument("--batch_size", type=int, default=8)
    parser.add_argument("--learning_rate", type=float, default=1e-6)
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu")
    parser.add_argument("--dtype", type=str, default="bfloat16")
    parser.add_argument("--use_wandb", action="store_true")
    parser.add_argument("--wandb_project", type=str, default="Improved llm model")
    parser.add_argument("--num_workers", type=int, default=1)
    parser.add_argument("--ddp", action="store_true")
    parser.add_argument("--accumulation_steps", type=int, default=1)
    parser.add_argument("--grad_clip", type=float, default=1.0)
    parser.add_argument("--warmup_iters", type=int, default=0)
    parser.add_argument("--log_interval", type=int, default=1)
    parser.add_argument("--save_interval", type=int, default=50)
    parser.add_argument('--local_rank', type=int, default=-1)
    parser.add_argument('--hidden_size', default=512, type=int)
    parser.add_argument('--num_hidden_layers', default=8, type=int)
    parser.add_argument('--max_seq_len', default=1024, type=int)
    parser.add_argument('--use_moe', default=False, type=bool)
    parser.add_argument("--data_path", type=str, default="../dataset/r1_mix_1024.jsonl")
    parser.add_argument("--val_ratio", type=float, default=0.1, help="Validation set ratio")
    parser.add_argument("--eval_interval", type=int, default=100, help="Evaluation interval steps")

    args = parser.parse_args()

    lm_config = MainConfig(hidden_size=args.hidden_size, num_hidden_layers=args.num_hidden_layers,
                         use_moe=args.use_moe)
    args.save_dir = os.path.join(args.out_dir)
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.out_dir, exist_ok=True)
    tokens_per_iter = args.batch_size * args.max_seq_len
    device_type = "cuda" if "cuda" in args.device else "cpu"

    args.wandb_run_name = f"Main-Distill-Reasoning-Epoch-{args.epochs}-BatchSize-{args.batch_size}-LearningRate-{args.learning_rate}"

    ctx = nullcontext() if device_type == "cpu" else torch.cuda.amp.autocast()
    ddp = int(os.environ.get("RANK", -1)) != -1  # is this a ddp run?
    ddp_local_rank, DEVICE = 0, "cuda:0"
    base_seed = 1337
    torch.manual_seed(base_seed)
    torch.cuda.manual_seed(base_seed)

    if ddp:
        init_distributed_mode()
        args.device = torch.device(DEVICE)
        rank = dist.get_rank()
        torch.manual_seed(base_seed + rank)
        # 同时设置 CUDA 的随机种子
        torch.cuda.manual_seed(base_seed + rank)

    if args.use_wandb and (not ddp or ddp_local_rank == 0):
        import wandb

        wandb.init(project=args.wandb_project, name=args.wandb_run_name)
    else:
        wandb = None

    model, tokenizer = init_model(lm_config)

    # 创建数据集并分割训练/验证集
    full_dataset = SFTDataset(args.data_path, tokenizer, max_length=args.max_seq_len)

    if args.val_ratio > 0:
        val_size = int(len(full_dataset) * args.val_ratio)
        train_size = len(full_dataset) - val_size
        train_ds, val_ds = random_split(full_dataset, [train_size, val_size])
        Logger(f"Dataset split: {train_size} training samples, {val_size} validation samples")
    else:
        train_ds = full_dataset
        val_ds = None
        Logger(f"Using full dataset for training: {len(train_ds)} samples")

    train_sampler = DistributedSampler(train_ds) if ddp else None
    train_loader = DataLoader(
        train_ds,
        batch_size=args.batch_size,
        pin_memory=True,
        drop_last=False,
        shuffle=False if ddp else True,
        num_workers=args.num_workers,
        sampler=train_sampler
    )

    val_loader = None
    if val_ds is not None:
        val_sampler = DistributedSampler(val_ds, shuffle=False) if ddp else None
        val_loader = DataLoader(
            val_ds,
            batch_size=args.batch_size,
            pin_memory=True,
            drop_last=False,
            shuffle=False,
            num_workers=args.num_workers,
            sampler=val_sampler
        )

    scaler = torch.cuda.amp.GradScaler(enabled=(args.dtype in ['float16', 'bfloat16']))
    optimizer = optim.AdamW(model.parameters(), lr=args.learning_rate)

    if ddp:
        model._ddp_params_and_buffers_to_ignore = {"pos_cis"}
        model = DistributedDataParallel(model, device_ids=[ddp_local_rank])

    iter_per_epoch = len(train_loader)
    Logger(f"Starting reasoning model training for {args.epochs} epochs, {iter_per_epoch} steps per epoch")

    for epoch in range(args.epochs):
        if ddp and train_sampler:
            train_sampler.set_epoch(epoch)
        train_epoch(epoch, wandb, val_loader)

    Logger("Reasoning model training completed!")
