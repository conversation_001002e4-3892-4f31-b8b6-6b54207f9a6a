name: gpt-training
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  - python=3.10
  - pip
  - pytorch==2.5.1
  - torchvision==0.20.1
  - torchaudio==2.5.1
  - pytorch-cuda=11.8
  - numpy=1.26.4
  - pandas
  - scikit-learn
  - matplotlib
  - jupyterlab
  - ipykernel
  - tqdm
  - pip:
      - "transformers[torch]"
      - datasets==2.19.0
      - deepspeed==0.14.2
      - peft==0.11.1
      - accelerate==0.30.1
      - bitsandbytes
      - trl==0.8.6
      - evaluate
      - sentencepiece
